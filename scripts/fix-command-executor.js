#!/usr/bin/env node

/**
 * Script to fix missing commandExecutor parameters
 */

import fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CommandExecutorFixer {
  constructor() {
    this.toolsDir = path.join(__dirname, "..", "src", "tools");
  }

  /**
   * Run the fix process
   */
  async fix() {
    console.log("🔧 Fixing missing commandExecutor parameters...\n");

    try {
      await this.processAllToolFiles();
      console.log("✅ Fixes completed successfully!");
    } catch (error) {
      console.error("❌ Fixes failed:", error.message);
      process.exit(1);
    }
  }

  /**
   * Process all tool files
   */
  async processAllToolFiles() {
    const toolDirs = await fs.readdir(this.toolsDir);
    
    for (const dir of toolDirs) {
      const dirPath = path.join(this.toolsDir, dir);
      const stats = await fs.stat(dirPath);
      
      if (stats.isDirectory()) {
        await this.processToolDirectory(dir, dirPath);
      }
    }
  }

  /**
   * Process a specific tool directory
   */
  async processToolDirectory(dirName, dirPath) {
    console.log(`  📁 Processing ${dirName}...`);
    
    const indexPath = path.join(dirPath, "index.ts");
    
    try {
      const content = await fs.readFile(indexPath, "utf-8");
      const fixed = this.fixFileContent(content);
      
      if (fixed.changed) {
        await fs.writeFile(indexPath, fixed.content);
        console.log(`    ✅ Fixed ${dirName}/index.ts`);
      } else {
        console.log(`    ℹ️  No fixes needed for ${dirName}/index.ts`);
      }
    } catch (error) {
      console.log(`    ❌ Error processing ${dirName}/index.ts: ${error.message}`);
    }
  }

  /**
   * Fix missing commandExecutor parameters
   */
  fixFileContent(content) {
    let fixed = content;
    let changed = false;

    // Fix getProjectInfo calls
    const projectInfoPattern = /getProjectInfo\(\s*([^,)]+)\s*\)/g;
    const projectInfoMatches = [...content.matchAll(projectInfoPattern)];
    
    for (const match of projectInfoMatches) {
      const fullMatch = match[0];
      const pathParam = match[1];
      const replacement = `getProjectInfo(${pathParam}, server.commandExecutor)`;
      
      if (!fullMatch.includes('server.commandExecutor')) {
        fixed = fixed.replace(fullMatch, replacement);
        changed = true;
      }
    }

    // Fix getWorkspaceInfo calls
    const workspaceInfoPattern = /getWorkspaceInfo\(\s*([^,)]+)\s*\)/g;
    const workspaceInfoMatches = [...content.matchAll(workspaceInfoPattern)];
    
    for (const match of workspaceInfoMatches) {
      const fullMatch = match[0];
      const pathParam = match[1];
      const replacement = `getWorkspaceInfo(${pathParam}, server.commandExecutor)`;
      
      if (!fullMatch.includes('server.commandExecutor')) {
        fixed = fixed.replace(fullMatch, replacement);
        changed = true;
      }
    }

    return {
      content: fixed,
      changed
    };
  }
}

// Run fixes if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const fixer = new CommandExecutorFixer();
  fixer.fix().catch(console.error);
}

export { CommandExecutorFixer };
