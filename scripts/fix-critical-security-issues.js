#!/usr/bin/env node

/**
 * Critical Security Fix Script
 * Fixes all command injection vulnerabilities by replacing execAsync with SecureCommandExecutor
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SecurityFixer {
  constructor() {
    this.srcDir = path.join(__dirname, "..", "src");
    this.fixedFiles = [];
    this.errors = [];
  }

  /**
   * Run the security fixes
   */
  async fix() {
    console.log("🔒 Starting critical security fixes...\n");

    const files = await this.getTypeScriptFiles();

    for (const file of files) {
      try {
        await this.fixFile(file);
      } catch (error) {
        this.errors.push({ file, error: error.message });
        console.error(`❌ Error fixing ${file}: ${error.message}`);
      }
    }

    this.generateReport();
  }

  /**
   * Fix a single file
   */
  async fixFile(filePath) {
    const content = fs.readFileSync(filePath, "utf8");
    let fixed = content;
    const fixes = [];

    // Check if file needs fixing
    const hasExecAsync = /execAsync\s*\(/.test(content);
    const hasSecureCommandExecutor = /SecureCommandExecutor/.test(content);

    if (!hasExecAsync) {
      return; // No execAsync calls to fix
    }

    // Add SecureCommandExecutor import if not present
    if (!hasSecureCommandExecutor) {
      // Find existing imports
      const importMatch = fixed.match(
        /import.*from.*["']\.\.\/\.\.\/server\.js["'];?\s*\n/
      );
      if (importMatch) {
        // Add import after server import
        const importLine = importMatch[0];
        const newImport = `${importLine}import { SecureCommandExecutor } from "../../utils/commandExecutor.js";\n`;
        fixed = fixed.replace(importLine, newImport);
        fixes.push("Added SecureCommandExecutor import");
      } else {
        // Add import at the top
        const firstImport = fixed.match(/^import.*$/m);
        if (firstImport) {
          const insertPoint = fixed.indexOf(firstImport[0]);
          fixed =
            fixed.slice(0, insertPoint) +
            `import { SecureCommandExecutor } from "../../utils/commandExecutor.js";\n` +
            fixed.slice(insertPoint);
          fixes.push("Added SecureCommandExecutor import at top");
        }
      }
    }

    // Remove execAsync imports and declarations
    fixed = fixed.replace(
      /import\s*\{\s*[^}]*promisify[^}]*\}\s*from\s*["']util["'];\s*\n/g,
      ""
    );
    fixed = fixed.replace(
      /import\s*\{\s*[^}]*exec[^}]*\}\s*from\s*["']child_process["'];\s*\n/g,
      ""
    );
    fixed = fixed.replace(
      /const\s+execAsync\s*=\s*promisify\(exec\);\s*\n/g,
      ""
    );

    // Fix all execAsync patterns

    // Pattern 1: const { stdout, stderr } = await execAsync(...)
    fixed = fixed.replace(
      /const\s*\{\s*stdout(?:\s*,\s*stderr)?\s*\}\s*=\s*await\s+execAsync\s*\([^)]+\);?/g,
      (match) => {
        return match.replace(
          "execAsync",
          "server.commandExecutor.executeString"
        );
      }
    );

    // Pattern 2: const { stdout: varName } = await execAsync(...)
    fixed = fixed.replace(
      /const\s*\{\s*stdout:\s*\w+\s*\}\s*=\s*await\s+execAsync\s*\([^)]+\);?/g,
      (match) => {
        return match.replace(
          "execAsync",
          "server.commandExecutor.executeString"
        );
      }
    );

    // Pattern 3: await execAsync(...)
    fixed = fixed.replace(/await\s+execAsync\s*\([^)]+\)/g, (match) => {
      return match.replace("execAsync", "server.commandExecutor.executeString");
    });

    // Pattern 4: execAsync(...) without await
    fixed = fixed.replace(/(?<!await\s+)execAsync\s*\([^)]+\)/g, (match) => {
      return match.replace("execAsync", "server.commandExecutor.executeString");
    });

    if (fixes.length > 0) {
      fs.writeFileSync(filePath, fixed, "utf8");
      this.fixedFiles.push({
        file: path.relative(process.cwd(), filePath),
        fixes,
      });
      console.log(`✅ Fixed ${path.relative(process.cwd(), filePath)}`);
    }
  }

  /**
   * Get all TypeScript files in the src directory
   */
  async getTypeScriptFiles() {
    const files = [];

    const scanDir = (dir) => {
      const entries = fs.readdirSync(dir);

      for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDir(fullPath);
        } else if (entry.endsWith(".ts")) {
          files.push(fullPath);
        }
      }
    };

    scanDir(this.srcDir);
    return files;
  }

  /**
   * Generate and display the fix report
   */
  generateReport() {
    console.log("\n🔒 Security Fix Report");
    console.log("======================\n");

    if (this.fixedFiles.length === 0 && this.errors.length === 0) {
      console.log("✅ No security issues found!");
      return;
    }

    if (this.fixedFiles.length > 0) {
      console.log(`✅ Fixed ${this.fixedFiles.length} files:\n`);

      this.fixedFiles.forEach((item, index) => {
        console.log(`${index + 1}. ${item.file}`);
        item.fixes.forEach((fix) => {
          console.log(`   - ${fix}`);
        });
        console.log("");
      });
    }

    if (this.errors.length > 0) {
      console.log(`❌ Errors in ${this.errors.length} files:\n`);

      this.errors.forEach((item, index) => {
        console.log(`${index + 1}. ${item.file}: ${item.error}`);
      });
      console.log("");
    }

    console.log("🔧 Next Steps:");
    console.log("1. Run 'npm run build' to verify TypeScript compilation");
    console.log("2. Run 'node scripts/security-audit.js' to verify fixes");
    console.log("3. Test the server functionality");
    console.log("\n✨ Critical security vulnerabilities have been addressed!");
  }
}

// Run the fixer
const fixer = new SecurityFixer();
fixer.fix().catch(console.error);
